<template>
	<view>
		<cu-custom bgColor="none" :isSearch="false" :isBack="true">
			<view slot="backText">返回</view>
			<view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx"></view>
		</cu-custom>

		<view class="map-container">

			<!-- 地图组件 -->
			<map id="map" class="map" :longitude="currentLocation.longitude" :latitude="currentLocation.latitude"
				:scale="12" :markers="markers" :show-location="true" @markertap="onMarkerTap"
				@callouttap="onCalloutTap">
			</map>

			<!-- 加载提示 -->
			<view v-if="loading" class="loading-mask">
				<view class="loading-content">
					<text class="loading-text">正在获取位置信息...</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();
var http = require("@/yl_welore/util/http.js");
export default {
	data() {
		return {
			loading: true,
			// 当前位置
			currentLocation: {
				longitude: 116.397428, // 默认北京坐标
				latitude: 39.90923
			},
			// 地图标注点数据
			markers: [],
			// 静态圈子数据
			realmData: []
		}
	},
	onLoad() {
		this.getCurrentLocation();
	},
	methods: {
		// 获取当前位置
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					console.log('获取位置成功:', res);
					this.currentLocation = {
						longitude: res.longitude,
						latitude: res.latitude
					};
					this.initMarkers(res);
					this.loading = false;
				},
				fail: (err) => {
					console.log('获取位置失败:', err);
					this.loading = false;

					// 弹窗提示用户授权位置
					uni.showModal({
						title: '位置权限',
						content: '为了更好地为您提供服务，需要获取您的位置信息。请在设置中开启位置权限。',
						confirmText: '去设置',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 用户点击去设置，打开设置页面
								uni.openSetting({
									success: (settingRes) => {
										console.log('设置结果:', settingRes);
										if (settingRes.authSetting['scope.userLocation']) {
											// 用户已授权，重新获取位置
											this.getCurrentLocation();
										} else {
											// 用户仍未授权，使用默认位置
											this.useDefaultLocation();
										}
									},
									fail: () => {
										// 打开设置失败，使用默认位置
										this.useDefaultLocation();
									}
								});
							} else {
								// 用户选择使用默认位置
								this.useDefaultLocation();
							}
						}
					});
				}
			});
		},

		// 使用默认位置
		useDefaultLocation() {
			uni.showToast({
				title: '未能获取用户位置',
				icon: 'none'
			});
			//this.initMarkers();
		},

		// 初始化地图标注点
		initMarkers(it) {
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.latitude = it.latitude;
			params.longitude = it.longitude;
			params.count = 10000;
			var b = app.globalData.api_root + 'Nameplate/get_nearby_list';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						this.markers = res.data.data.map((item) => {
							return {
								id: item.id,
								longitude: item.longitude,
								latitude: item.latitude,
								iconPath: item.realm_icon,
								width: 40,
								height: 40,
								anchor: {
									x: 0.5,
									y: 1
								},
								callout: {
									content: item.realm_name,
									color: '#333333',
									fontSize: 14,
									borderRadius: 100,
									bgColor: '#ffffff',
									padding: 8,
									display: 'ALWAYS',
									textAlign: 'center'
								}
							};
						});
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					});
				}
			});

			console.log('初始化标注点:', this.markers);
		},

		// 标注点点击事件
		onMarkerTap(e) {
			console.log('标注点被点击:', e);
			const markerId = e.detail.markerId;
			console.log('点击的标注点ID:', markerId);
			this.showMarkerActionSheet(markerId);
		},

		// 气泡点击事件
		onCalloutTap(e) {
			console.log('气泡被点击:', e);
			const markerId = e.detail.markerId;
			console.log('点击的气泡ID:', markerId);
			this.navigateToRealm(markerId);
		},

		// 显示标注点操作选择表
		showMarkerActionSheet(markerId) {
			uni.showActionSheet({
				itemList: ['进入圈子', '开启导航'],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 进入圈子
						this.navigateToRealm(markerId);
					} else if (res.tapIndex === 1) {
						// 开启导航
						this.openNavigation(markerId);
					}
				},
				fail: (err) => {
					console.log('操作选择表取消或失败:', err);
				}
			});
		},

		// 根据markerId获取marker信息
		getMarkerById(markerId) {
			return this.markers.find(marker => marker.id == markerId);
		},

		// 开启导航
		openNavigation(markerId) {
			const marker = this.getMarkerById(markerId);
			if (marker) {
				uni.openLocation({
					latitude: marker.latitude,
					longitude: marker.longitude,
					name: marker.callout.content, // 使用圈子名称
					success: () => {
						console.log('导航开启成功');
					},
					fail: (err) => {
						console.log('导航开启失败:', err);
						uni.showToast({
							title: '导航开启失败',
							icon: 'none'
						});
					}
				});
			} else {
				uni.showToast({
					title: '未找到位置信息',
					icon: 'none'
				});
			}
		},

		// 跳转到圈子详情
		navigateToRealm(markerId) {
			uni.navigateTo({
				url: `/yl_welore/pages/packageA/circle_info/index?id=${markerId}`
			});
		}
	}
}
</script>

<style scoped>
.map-container {
	width: 100%;
	height: 100%;
	top: 0;
	position: fixed;
}

.map {
	width: 100%;
	height: 100%;
}

.loading-mask {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 999;
}

.loading-content {
	background-color: #ffffff;
	padding: 20px;
	border-radius: 8px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-text {
	color: #333333;
	font-size: 16px;
}
</style>
